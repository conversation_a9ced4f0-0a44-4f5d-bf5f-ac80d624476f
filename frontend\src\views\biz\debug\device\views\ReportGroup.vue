<template>
  <v-contextmenu ref="contextmenu">
    <v-contextmenu-item @click="uploadWave">{{ t("device.reportGroup.contextMenu.uploadWave") }}</v-contextmenu-item>
    <v-contextmenu-divider></v-contextmenu-divider>
    <v-contextmenu-item @click="searchReport">{{ t("device.reportGroup.contextMenu.getHistoryReport") }}</v-contextmenu-item>
    <v-contextmenu-item @click="exportReport">{{ t("device.reportGroup.contextMenu.saveResult") }}</v-contextmenu-item>
    <v-contextmenu-divider></v-contextmenu-divider>
    <v-contextmenu-item @click="clearList">{{ t("device.reportGroup.contextMenu.clearContent") }}</v-contextmenu-item>
  </v-contextmenu>
  <div class="table-main report-page">
    <div class="flex flex-wrap header card button-group">
      <el-form :inline="true" class="report-query-form" style="width: 100%">
        <el-form-item>
          <el-checkbox v-model="isDateCheck">{{ t("device.reportGroup.date") }}：</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="dateRange" type="datetimerange"></el-date-picker>
        </el-form-item>
      </el-form>
      <div class="header-button-ri" style="width: 100%; margin-top: 4px; text-align: right">
        <el-button type="primary" :icon="Search" :disabled="isButtonClick" @click="searchReport">{{ t("device.reportGroup.search") }}</el-button>
        <el-button type="success" :icon="Folder" :disabled="isButtonClick" @click="exportReport">{{ t("device.reportGroup.save") }}</el-button>
        <el-button type="primary" plain :icon="Refresh" @click="refreshReport" :loading="refreshLoading">{{ refreshName }}</el-button>
        <el-button type="danger" plain :icon="Delete" @click="clearList">{{ t("device.reportGroup.clearList") }}</el-button>
      </div>
    </div>
    <div class="card table-box report-table">
      <el-table
        v-loading="tableLoad"
        v-contextmenu:contextmenu="contextmenu"
        border
        :row-key="isTreeData ? 'id' : 'faultNo'"
        :data="tableData"
        :max-height="getTableMaxHeight(220)"
        highlight-current-row
        :default-expand-all="isTreeData"
        :tree-props="isTreeData ? { children: 'children', hasChildren: 'hasChildren' } : undefined"
        :show-header="true"
        :stripe="true"
        :element-loading-text="t('device.reportGroup.loading')"
        @cell-contextmenu="cellContextmenu"
        @cell-click="cellContextmenu"
      >
        <!-- 根据数据类型显示不同的列 -->
        <template v-if="isTreeData">
          <!-- 树形数据显示 -->
          <el-table-column :label="t('device.reportGroup.table.reportId')" width="240" prop="name" align="center"></el-table-column>
          <el-table-column :label="t('device.reportGroup.table.time')" width="300" prop="ret_ms" align="center"></el-table-column>
          <el-table-column :label="t('device.reportGroup.table.description')" prop="value" align="center"></el-table-column>
        </template>
        <template v-else>
          <!-- 平面数据显示 -->
          <el-table-column :label="t('device.reportGroup.table.reportId')" width="240" prop="faultNo" align="center"></el-table-column>
          <el-table-column :label="t('device.reportGroup.table.time')" width="300" prop="faultStartTime" align="center"></el-table-column>
          <el-table-column :label="t('device.reportGroup.table.description')" prop="st" align="center">
            <template #default="scope">
              <span>{{ scope.row.st?.length || 0 }} ST + {{ scope.row.mx?.length || 0 }} MX</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
  </div>
  <el-dialog
    v-model="dialogShow.searchProgress"
    width="60%"
    class="hover"
    :align-center="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :show-close="false"
    draggable
    :title="t('device.reportGroup.progress.title')"
  >
    <div style="height: 120px; padding: 15px">
      <el-progress :percentage="dialogShow.percentage" :text-inside="false" striped striped-flow style="margin-top: 60px">
        <span>{{ dialogShow.progressText }}</span>
      </el-progress>
    </div>
    <!--    <template #footer>-->
    <!--      <div v-if="isUpload" style="margin-right: 5px; margin-bottom: 5px">-->
    <!--        <el-button @click="cancelUpload">取消</el-button>-->
    <!--      </div>-->
    <!--    </template>-->
  </el-dialog>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>
<script setup lang="ts">
import { Delete, Refresh, Search, Folder } from "@element-plus/icons-vue";
import Message from "@/scripts/message";
import { ipc } from "@/api/request/ipcRenderer";
import { getDateZh, getTableMaxHeight } from "@/utils/index";
import { genRpcTimeParam } from "@/utils/iec/iecRpcUtils";
import { IECNotify, RealEventState, ReportParam, ResultData } from "@/api";
import { ContextmenuInstance } from "v-contextmenu/es/types";
import { ref, computed, nextTick } from "vue";
import { useDebugStore } from "@/stores/modules/debug";
import { reportApi } from "@/api/modules/biz/debug/report";
import { realEventApi } from "@/api/modules/biz/debug/realevent";
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
import { osControlApi } from "@/api/modules/biz/os";
import { cloneDeep, isEmpty } from "lodash";
import { ElMessageBox } from "element-plus";
import { useConfigStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";
import ProgressDialog from "../dialog/ProgressDialog.vue";
const progressDialog = ref();
const { t } = useI18n();
const realEventState = ref<RealEventState>({
  subscribe: false,
  type: ""
});
const refreshLoading = ref(false);
const { report, currDevice, addConsole } = useDebugStore();
const { sysInfo } = useConfigStore();
const { paramInfo } = useConfigStore();
const filePath = ref("");
const globalReport = report.get(currDevice.id);

// 初始化查询条件
const getDefaultDateRange = (): [Date, Date] => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return [yesterday, new Date()];
};

const dateRange = ref<[Date, Date]>(getDefaultDateRange());
const isDateCheck = ref<boolean>(false);
const isButtonClick = ref(false);
const contextmenu = ref<ContextmenuInstance>();
const dialogShow = ref({
  searchProgress: false,
  percentage: 0,
  progressText: ""
});
let currLine: any = undefined;
const cellContextmenu = (row): void => {
  currLine = row;
};
const showHiddenMark = ref<boolean>(false);
// showHiddenDesc 已移除，因为在模板中未使用
const isUpload = ref<boolean>(false);
const tableLoad = ref<boolean>(false);
const tableData = ref<any[]>([]);
const refreshMark = ref<boolean>(false);
const refreshName = computed(() => (refreshMark.value ? t("device.reportGroup.refresh.stop") : t("device.reportGroup.refresh.start")));

// 判断是否为树形数据
const isTreeData = computed(() => {
  if (tableData.value.length === 0) return false;
  const firstItem = tableData.value[0];
  // 树形数据包含 id, name, value, ret_ms, children 字段
  // 平面数据包含 faultNo, faultStartTime, st, mx 字段
  return firstItem && typeof firstItem.id !== "undefined" && typeof firstItem.name !== "undefined" && typeof firstItem.value !== "undefined";
});

const getTableData = (): any[] => {
  return globalReport!.groupReport.get(globalReport?.newname || globalReport?.currReportType || "") || [];
};

// 保存查询条件到缓存
const saveQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  globalReport.queryConditions.set(reportKey, {
    isDateCheck: isDateCheck.value,
    dateRange: [new Date(dateRange.value[0]), new Date(dateRange.value[1])],
    searchInfo: "",
    searchType: 0
  });
};

// 从缓存恢复查询条件
const restoreQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  const cached = globalReport.queryConditions.get(reportKey);
  if (cached) {
    isDateCheck.value = cached.isDateCheck;
    dateRange.value = [new Date(cached.dateRange[0]), new Date(cached.dateRange[1])];
  } else {
    // 如果没有缓存，使用默认值
    isDateCheck.value = false;
    dateRange.value = getDefaultDateRange();
  }
};
const uploadWave = async (): Promise<void> => {
  if (getTableData().length == 0) {
    Message.warning(t("device.reportGroup.messages.noFileToUpload"));
    return;
  }
  let fileNo = getTableData()[0]?.faultNo;
  if (currLine) {
    fileNo = currLine.faultNo;
  }
  const arg = { fileNo: fileNo, time: currLine?.faultStartTime };
  const res = await reportApi.uploadWaveByDevice(props.deviceId, arg);
  if (res.code != 1) {
    Message.warning(res.msg);
    return;
  }
  filePath.value = res.data;
};

// const cancelUpload = async (): Promise<void> => {
//   const arg = undefined;
//   const res = await reportApi.cancelUpload(arg);
//   if (res.code != 1) {
//     Message.warning(res.msg);
//     return;
//   }
// };
let timerId;
const timerRefresh = (): void => {
  timerId = setTimeout(() => {
    refreshList().then(() => {
      timerRefresh();
    });
  }, paramInfo.REPORT_REFRESH_TIME);
};

const refreshReport = (): void => {
  refreshMark.value = !refreshMark.value;
  if (refreshMark.value == true) {
    subRealEvent();
  } else {
    unSubRealEvent();
  }
};

const clearList = (): void => {
  globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  tableData.value = [];
};

const searchReport = async (): Promise<void> => {
  if (isDateCheck.value && dateRange.value.length < 2) {
    Message.warning(t("device.reportGroup.messages.selectDateRange"));
    return;
  }

  // 保存查询条件到缓存
  saveQueryConditions();

  const arg: ReportParam.IECRpcGroupReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: isDateCheck.value ? genRpcTimeParam(dateRange.value[0]) : "",
    stopTime: isDateCheck.value ? genRpcTimeParam(dateRange.value[1]) : "",
    faultAfter: ""
  };
  globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  isUpload.value = false;
  tableLoad.value = true;
  isButtonClick.value = true;
  dialogShow.value.percentage = 0;
  dialogShow.value.searchProgress = true; // 弹窗立即显示
  const reportDesc: string = globalReport!.currReportDesc;
  dialogShow.value.progressText = t("device.reportGroup.progress.searching", { type: reportDesc });
  startFakeProgress();
  // 记录开始时间
  const start = Date.now();
  const res: ResultData<any> = await reportApi.getGroupReportListByDevice(props.deviceId, arg);
  // 计算已用时，若不足500ms则补足
  const elapsed = Date.now() - start;
  if (elapsed < 500) {
    await new Promise(resolve => setTimeout(resolve, 500 - elapsed));
  }
  stopFakeProgress();
  dialogShow.value.searchProgress = false;
  if (res.code != 1) {
    Message.warning(res.msg);
    tableLoad.value = false;
    isButtonClick.value = false;
    return;
  }
};

const refreshList = async (): Promise<void> => {
  const arg: ReportParam.IECRpcGroupReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: "",
    stopTime: "",
    faultAfter: ""
  };
  const res = await reportApi.refreshGroupReportByDevice(props.deviceId, arg);
  if (res.code != 1) {
    Message.warning(res.msg);
    return;
  }

  console.log("[ReportGroup] refreshGroupReport 返回数据:", res.data);
  console.log("[ReportGroup] 数据类型:", typeof res.data, "是否为数组:", Array.isArray(res.data));
  if (Array.isArray(res.data) && res.data.length > 0) {
    console.log("[ReportGroup] 第一条数据结构:", res.data[0]);
    console.log("[ReportGroup] 第一条数据字段:", Object.keys(res.data[0]));
  }

  globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  if (Array.isArray(res.data)) {
    let filteredList = res.data as any;
    if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
      const keyword = globalReport!.keyword.toLowerCase();
      filteredList = res.data.filter(item => {
        // 判断数据类型并使用相应的字段进行过滤
        if (item.id !== undefined && item.name !== undefined) {
          // 树形数据：搜索 name, value, ret_ms 字段
          const name = (item.name || "").toString().toLowerCase();
          const value = (item.value || "").toString().toLowerCase();
          const ret_ms = (item.ret_ms || "").toString().toLowerCase();
          return name.includes(keyword) || value.includes(keyword) || ret_ms.includes(keyword);
        } else {
          // 平面数据：搜索 faultNo, faultStartTime 字段
          const faultNo = (item.faultNo || "").toString().toLowerCase();
          const faultStartTime = (item.faultStartTime || "").toLowerCase();
          return faultNo.includes(keyword) || faultStartTime.includes(keyword);
        }
      });
    }

    console.log("[ReportGroup] 设置表格数据:", filteredList);
    console.log("[ReportGroup] 过滤后数据量:", filteredList.length);
    if (filteredList.length > 0) {
      console.log("[ReportGroup] 第一条过滤后数据:", filteredList[0]);
    }

    tableData.value = filteredList;
    globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", filteredList);

    console.log("[ReportGroup] tableData.value 设置完成，长度:", tableData.value.length);
    console.log("[ReportGroup] isTreeData 计算结果:", isTreeData.value);
  } else {
    console.log("[ReportGroup] 没有数据或数据不是数组");
    globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
    tableData.value = [];
  }
};

let fakeProgressTimer: any = null;
function startFakeProgress() {
  dialogShow.value.percentage = 0;
  if (fakeProgressTimer) clearInterval(fakeProgressTimer);
  fakeProgressTimer = setInterval(() => {
    if (dialogShow.value.percentage < 95) {
      dialogShow.value.percentage += 5;
    }
  }, 100);
}
function stopFakeProgress() {
  if (fakeProgressTimer) {
    clearInterval(fakeProgressTimer);
    fakeProgressTimer = null;
  }
  dialogShow.value.percentage = 100;
}

const exportReport = async (): Promise<void> => {
  if (getTableData().length == 0) {
    Message.warning(t("report.group.messages.noDataToSave"));
    addConsole(t("report.exportLogFailed", { msg: t("report.group.messages.noDataToSave") }));
    return;
  }

  // 第一步：先选择保存路径，不显示进度条
  const reportDesc: string = globalReport!.currReportDesc;
  const path = await osControlApi.openSaveFileDialogByParams({
    title: t("report.group.messages.saveReport"),
    defaultPath: reportDesc + getDateZh(),
    filterList: [{ name: "Rpt", extensions: ["rpt"] }]
  });

  // 如果用户取消选择路径，直接返回，不显示任何错误信息
  if (!path) {
    addConsole(t("report.exportLogCancelled"));
    return;
  }

  // 第二步：用户确认路径后，才显示进度条并开始导出
  progressDialog.value.show();
  progressDialog.value.setProgress(5, t("report.exporting"), false);
  let fakeProgress = setInterval(() => {
    if (progressDialog.value.progressDialog.percentage < 95) {
      progressDialog.value.setProgress(progressDialog.value.progressDialog.percentage + 5, t("report.exporting"));
    }
  }, 100);
  const exportList: any[] = [];
  getTableData().forEach((obj: any) => {
    exportList.push({
      name: `故障${obj.faultNo}`,
      value: obj.faultStartTime,
      ret_ms: `${obj.st?.length || 0} ST + ${obj.mx?.length || 0} MX`,
      children: obj.children
    });
  });
  const arg: ReportParam.IECRpcCommonReportExportParam = {
    type: globalReport!.currReportType,
    method: globalReport!.currReportMethod,
    path: path as unknown as string,
    items: cloneDeep(exportList)
  };
  const res: ResultData<any> = await reportApi.exportCommonReportByDevice(props.deviceId, arg);
  clearInterval(fakeProgress);
  progressDialog.value.setProgress(100, t("report.exporting"));
  setTimeout(() => progressDialog.value.hide(), 500);
  if (res.code != 1) {
    Message.warning(res.msg);
    addConsole(t("report.exportLogFailed", { msg: res.msg }));
    return;
  }
  Message.success(t("report.group.messages.saveSuccess"));
  addConsole(t("report.exportLogSuccess", { path }));
  await ElMessageBox.alert(t("report.group.messages.saveSuccess"), t("report.progress"), {
    confirmButtonText: t("common.confirm"),
    type: "success"
  });
};

const initTableData = (): void => {
  const cachedData = getTableData();
  if (cachedData.length > 0) {
    // 如果有缓存数据，先显示缓存数据
    dialogShow.value.percentage = 50;
    dialogShow.value.searchProgress = true;
    dialogShow.value.progressText = t("device.reportGroup.progress.loading");
    nextTick(() => {
      setTimeout(() => {
        tableData.value = cachedData;
        dialogShow.value.percentage = 100;
        dialogShow.value.searchProgress = false;
      }, 50);
    });
  } else {
    // 如果没有缓存数据，主动获取树形数据
    refreshList();
  }
};

const openWaveFile = (path: string) => {
  ElMessageBox.confirm(t("device.reportGroup.messages.openWaveFileConfirm"), t("device.reportGroup.messages.openWaveFileTitle"), {
    confirmButtonText: t("device.reportGroup.messages.confirm"),
    cancelButtonText: t("device.reportGroup.messages.cancel"),
    type: "success"
  }).then(async () => {
    if (isEmpty(sysInfo.WAVE_TOOL_PATH)) {
      Message.warning(t("device.reportGroup.messages.waveToolNotConfigured"));
      addConsole(t("device.reportGroup.messages.waveToolNotConfigured"));
      return;
    }
    const exePath = sysInfo.WAVE_TOOL_PATH;
    const filePath = path;
    const arg = { exePath: exePath, filePath: filePath };
    const res: ResultData<any> = await reportApi.openWaveFile(arg);
    if (res.code != 1) {
      Message.warning(res.msg);
      return;
    }
  });
};

const notifyMethod = (_event: unknown, notify: IECNotify): void => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;
  const reportData = notify.data as any;
  if (notify.type == "readGroupReport") {
    console.log(
      `[GroupReport UI] 处理故障报告通知 - isPartial: ${notify.isPartial}, 数据类型: ${reportData.data?.progress ? "进度" : "数据"}, 当前界面状态: isReportLoading=${globalReport!.isReportLoading}, searchProgress=${dialogShow.value.searchProgress}`
    );

    if (reportData.code != 1) {
      // 错误处理
      tableLoad.value = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;
      Message.warning(reportData.msg);
      console.log(`[GroupReport UI] 处理错误完成`);
      return;
    }

    // 检查是否为进度更新
    if (reportData.data?.progress?.isProgress) {
      // 这是进度更新，只更新进度条，不更新表格数据
      const progressInfo = reportData.data.progress;
      const currentCount = progressInfo.currentCount;
      const callbackCount = progressInfo.callbackCount;

      // 基于实际数据量计算进度
      let progressPercent = 0;
      if (currentCount > 0) {
        // 使用对数函数计算进度，避免进度跳跃过快
        progressPercent = Math.min(85, Math.floor(Math.log(currentCount + 1) * 10 + callbackCount * 2));
      } else {
        progressPercent = Math.min(20, callbackCount * 3);
      }

      // 确保进度不倒退
      progressPercent = Math.max(dialogShow.value.percentage, progressPercent);

      dialogShow.value.percentage = progressPercent;

      console.log(`[GroupReport UI] 进度更新完成 - 数据量: ${currentCount}条, 回调次数: ${callbackCount}, 进度: ${progressPercent}%`);
      return;
    }

    // 这是最终数据更新
    if (Array.isArray(reportData.data)) {
      console.log(`[GroupReport UI] 处理最终数据 - 原始数据量: ${reportData.data.length}条`);
      console.log(`[GroupReport UI] 数据样本:`, reportData.data.slice(0, 2)); // 显示前2条数据样本
      console.log(`[GroupReport UI] 当前关键字:`, globalReport!.keyword);

      let filteredList = reportData.data;
      if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
        const keyword = globalReport!.keyword.toLowerCase();
        filteredList = reportData.data.filter(item => {
          // 判断数据类型并使用相应的字段进行过滤
          if (item.id !== undefined && item.name !== undefined) {
            // 树形数据：搜索 name, value, ret_ms 字段
            const name = (item.name || "").toString().toLowerCase();
            const value = (item.value || "").toString().toLowerCase();
            const ret_ms = (item.ret_ms || "").toString().toLowerCase();
            const matches = name.includes(keyword) || value.includes(keyword) || ret_ms.includes(keyword);
            if (!matches && reportData.data.length <= 5) {
              console.log(`[GroupReport UI] 过滤项目: "${item.name} ${item.value}" 不匹配关键字 "${globalReport!.keyword}"`);
            }
            return matches;
          } else {
            // 平面数据：搜索 faultNo 和 faultStartTime 字段
            const faultNo = (item.faultNo || "").toString().toLowerCase();
            const faultStartTime = (item.faultStartTime || "").toLowerCase();
            const matches = faultNo.includes(keyword) || faultStartTime.includes(keyword);
            if (!matches && reportData.data.length <= 5) {
              console.log(`[GroupReport UI] 过滤项目: "故障${item.faultNo} ${item.faultStartTime}" 不匹配关键字 "${globalReport!.keyword}"`);
            }
            return matches;
          }
        });
        console.log(`[GroupReport UI] 数据过滤完成 - 原始数据: ${reportData.data.length}条, 过滤后: ${filteredList.length}条`);
      }

      console.log(`[GroupReport UI] 准备渲染数据 - tableData当前长度: ${tableData.value.length}, 新数据长度: ${filteredList.length}`);

      // 最终数据直接渲染到表格
      tableData.value = filteredList;
      globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", filteredList);

      console.log(`[GroupReport UI] 数据已设置 - tableData.value.length: ${tableData.value.length}`);
      console.log(`[GroupReport UI] 表格数据样本:`, tableData.value.slice(0, 2));

      // 强制触发Vue响应式更新
      nextTick(() => {
        console.log(`[GroupReport UI] nextTick后 - tableData.value.length: ${tableData.value.length}`);
        console.log(`[GroupReport UI] DOM更新后的表格状态检查完成`);

        // 验证数据字段
        if (tableData.value.length > 0) {
          const firstItem = tableData.value[0] as any;
          console.log(`[GroupReport UI] 第一条数据字段检查:`);
          if (firstItem.id !== undefined && firstItem.name !== undefined) {
            // 树形数据
            console.log(`  - 数据类型: 树形数据`);
            console.log(`  - id: "${firstItem.id}" (${typeof firstItem.id})`);
            console.log(`  - name: "${firstItem.name}" (${typeof firstItem.name})`);
            console.log(`  - value: "${firstItem.value}" (${typeof firstItem.value})`);
            console.log(`  - ret_ms: "${firstItem.ret_ms}" (${typeof firstItem.ret_ms})`);
            console.log(`  - children: ${firstItem.children?.length || 0}条 (${typeof firstItem.children})`);
          } else {
            // 平面数据
            console.log(`  - 数据类型: 平面数据`);
            console.log(`  - faultNo: "${firstItem.faultNo}" (${typeof firstItem.faultNo})`);
            console.log(`  - faultStartTime: "${firstItem.faultStartTime}" (${typeof firstItem.faultStartTime})`);
            console.log(`  - st: ${firstItem.st?.length || 0}条 (${typeof firstItem.st})`);
            console.log(`  - mx: ${firstItem.mx?.length || 0}条 (${typeof firstItem.mx})`);
          }
        }
      });

      // 完成加载
      globalReport!.isReportLoading = false;
      tableLoad.value = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;

      console.log(`[GroupReport UI] 最终数据处理完成 - 最终数据量: ${filteredList.length}条`);
    } else {
      // 空数据情况
      console.log(`[GroupReport UI] 处理空数据情况`);
      globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
      tableData.value = [];

      globalReport!.isReportLoading = false;
      tableLoad.value = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;

      console.log(`[GroupReport UI] 空数据处理完成`);
    }
    return;
  }
  if (notify.type == "fileUpload") {
    isUpload.value = true;
    dialogShow.value.searchProgress = true;
    dialogShow.value.percentage = reportData.percentage;
    dialogShow.value.progressText = t("device.reportGroup.messages.waveFileUploading");
    return;
  }
  if (notify.type == "fileUploadSuccess") {
    dialogShow.value.searchProgress = false;
    dialogShow.value.progressText = t("device.reportGroup.messages.waveFileUploadComplete");
    Message.success(t("device.reportGroup.messages.waveFileUploadComplete"));
    openWaveFile(reportData.data);
    addConsole(t("device.reportGroup.messages.waveFileUploadCompleteWithPath", { path: reportData.data }));
    return;
  }
};

onMounted(() => {
  ipc.on("report_notify", notifyMethod);
  initTableData();
});

onBeforeUnmount(() => {
  if (timerId) {
    clearTimeout(timerId);
  }
  ipc.removeAllListeners("report_notify");
});

watch(
  () => refreshMark.value,
  newValue => {
    if (newValue == true) {
      // refreshName现在是computed属性，会自动更新
      isButtonClick.value = true;
      timerRefresh();
    } else {
      // refreshName现在是computed属性，会自动更新
      isButtonClick.value = false;
      if (timerId) {
        clearTimeout(timerId);
      }
    }
  }
);
watch(
  () => showHiddenMark.value,
  () => {
    // 清空缓存
    globalReport!.groupReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
    refreshList();
  }
);
watch(
  () => globalReport!.newname,
  () => {
    const cachedData = getTableData();
    if (cachedData.length > 0) {
      // 如果有缓存数据，先显示缓存数据
      tableData.value = cachedData;
    } else {
      // 如果没有缓存数据，主动获取树形数据
      refreshList();
    }

    isButtonClick.value = false;
    refreshMark.value = false;

    // 恢复查询条件而不是重置
    restoreQueryConditions();

    // refreshName现在是computed属性，会自动更新
    if (timerId) {
      clearTimeout(timerId);
    }
    unSubRealEvent();
  }
);
const subRealEvent = async () => {
  if (realEventState.value.subscribe) {
    return;
  }
  refreshLoading.value = true;
  try {
    // 订阅事件
    const res = await realEventApi.subRealEventByDevice(props.deviceId, [globalReport!.currReportType]);
    if (res.code != 0) {
      Message.error(res.msg);
    } else {
      realEventState.value.subscribe = true;
      realEventState.value.type = globalReport!.currReportType;
    }
  } finally {
    refreshLoading.value = false;
  }
};
const unSubRealEvent = async () => {
  if (!realEventState.value.subscribe) {
    return;
  }
  refreshLoading.value = true;
  try {
    // 取消订阅
    const res = await realEventApi.unSubRealEventByDevice(props.deviceId, { type: [realEventState.value.type] });
    if (res.code != 0) {
      Message.error(res.msg);
    } else {
      realEventState.value.subscribe = false;
      realEventState.value.type = "";
    }
  } finally {
    refreshLoading.value = false;
  }
};
onUnmounted(() => {
  unSubRealEvent();
});
</script>
<style scoped lang="scss">
.report-page {
  margin-top: 5px;
  .button-group {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    .search-page {
      font-size: 14px;
    }
    .his-var {
      font-size: 14px;
    }
    .el-row {
      div {
        font-size: 14px;
        :deep(.el-date-editor) {
          width: 340px;
        }
      }
    }
  }
  .report-table {
    overflow-y: auto;
    scrollbar-width: none;
  }
}
.header {
  margin-bottom: 5px;
}
</style>
