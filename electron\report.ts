"use strict";
import {
  EventReportRequestData,
  OperateReportRequestData,
  FaultReportRequestData,
  EventReportRequestRes,
  EventReportEntry,
  FaultReportFaultEntry,
  FaultReportRequestRes,
  OperateReportEntry,
  OperateReportRequestRes,
  AddCauseDescMap,
  FileDirRequestData,
  UpadRpcFileItem,
  FileReadTransferData,
  FileReadRequestData,
  FileReadTransferStatus,
  AuditLogRequestRes,
  AuditLogEntry,
  AuditLogRequestData,
  DeviceProgramVersion,
} from "iec-upadrpc/dist/src/data";
import { logger } from "ee-core/log";
import { IECReq } from "../../interface/debug/request";
import { IECResult } from "iec-common/dist/data/iecdata";
import ClientDeviceGlobal from "../../data/debug/globalDeviceData";
import { getNoConnectResult } from "../../utils/common";
import IECCONSTANTS from "../../data/debug/iecConstants";
import { getMainWindow } from "ee-core/electron";
import { IECNotify, IEC_EVENT } from "../../data/debug/notify";
import { sendMessageToUIByNotify } from "../../utils/iecUiUtils";
import { undefinedToEmpty, writeDataToFile } from "../../utils/common";
import {
  FaultReportEntry,
  IECReportExportParam,
  IECRpcReportOpenWaveParam,
} from "../../interface/debug/report";
import * as XLSX from "xlsx";
import { isEmpty, cloneDeep, filter } from "lodash";
import { debugInfoMenuService } from "./debuginfomenu";
import Ps from "ee-core/ps";
import ChildProcess from "child_process";

/**
 * 报告相关Service
 * 负责历史报告、操作报告、故障报告的查询、导出、清除等业务逻辑
 * <AUTHOR>
 * @class
 */
class ReportService {
  // 获取通用报告
  async getCommonReportList(
    req: IECReq<EventReportRequestData>
  ): Promise<IECResult<any>> {
    // 获取通用报告方法入口日志
    logger.info(
      "[ReportService] getCommonReportList 入参:",
      JSON.stringify(req)
    );
    const { type, startTime, stopTime, entryAfter, orderBy } = req.data;
    const clientResult = new IECResult<EventReportEntry[]>();
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    // 取设备程序版本，默认V1版本
    let deviceVersion: DeviceProgramVersion = DeviceProgramVersion.VERSION_1;
    const deviceInfo = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    if (
      deviceInfo &&
      deviceInfo.debugInfo &&
      deviceInfo.debugInfo.configVersion
    ) {
      if (!deviceInfo.debugInfo.configVersion.toUpperCase().startsWith("V1.")) {
        deviceVersion = DeviceProgramVersion.VERSION_2;
      }
    }
    const reportList: EventReportEntry[] = [];
    try {
      await new Promise<void>((resolve, reject) => {
        client
          .getHistoryEventReport({
            type,
            startTime,
            stopTime,
            entryAfter,
            orderBy,
            cb: (value: EventReportRequestRes) => {
              logger.info(
                "getHistoryEventReport",
                type,
                startTime,
                stopTime,
                entryAfter,
                orderBy
              );
              value.entry.forEach((data) => {
                data.name =
                  genMenuDesc(req.head.id, data.name) +
                  " " +
                  data.oldVal +
                  "->" +
                  data.stVal;
              });
              reportList.push(...value.entry);
              logger.info("getHistoryEventReport", reportList.length);
              if (value.moreFollows === 0) {
                resolve();
              }
            },
          })
          .catch((err) => {
            clientResult.code = IECCONSTANTS.CODE_ERROR;
            clientResult.msg = "获取历史报告错误:" + err;
            reject(err);
          });
      });
      clientResult.code = IECCONSTANTS.CODE_SUCCESS;
      clientResult.msg = "获取成功";
      clientResult.data = reportList;
      const notify: IECNotify = {
        type: "readCommonReport",
        data: clientResult,
      };
      sendMessageToUIByNotify(IEC_EVENT.REPORT_NOTIFY, notify, getMainWindow());
    } catch (err) {
      // 错误已在 catch 里处理
    }
    logger.info("[ReportService] getCommonReportList 返回:", clientResult);
    return clientResult;
  }

  // 获取整组报告
  async getGroupReportList(
    req: IECReq<FaultReportRequestData>
  ): Promise<IECResult<any>> {
    const { startTime, stopTime, faultAfter } = req.data;
    const clientResult = new IECResult<FaultReportFaultEntry[]>();
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const reportList: FaultReportFaultEntry[] = [];
    try {
      await new Promise<void>((resolve, reject) => {
        client
          .getHistoryFaultReport({
            startTime,
            stopTime,
            faultAfter,
            cb: (value: FaultReportRequestRes) => {
              reportList.push(...value.faultEntry);
              if (value.moreFollows === 0) {
                resolve();
              }
            },
          })
          .catch((err) => {
            clientResult.code = IECCONSTANTS.CODE_ERROR;
            clientResult.msg = "获取历史报告错误:" + err;
            reject(err);
          });
      });
      clientResult.code = IECCONSTANTS.CODE_SUCCESS;
      clientResult.msg = "获取成功";
      clientResult.data = reportList;
      const notify: IECNotify = {
        type: "readGroupReport",
        data: clientResult,
      };
      sendMessageToUIByNotify(IEC_EVENT.REPORT_NOTIFY, notify, getMainWindow());
    } catch (err) {
      // 错误已在 catch 里处理
    }
    return clientResult;
  }

  // 获取操作报告
  async getOperateReportList(
    req: IECReq<OperateReportRequestData>
  ): Promise<IECResult<any>> {
    const { startTime, stopTime, entryAfter } = req.data;
    const clientResult = new IECResult<OperateReportEntry[]>();
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const reportList: OperateReportEntry[] = [];
    try {
      await new Promise<void>((resolve, reject) => {
        client
          .getHistoryOperateReport({
            startTime,
            stopTime,
            entryAfter,
            cb: (value: OperateReportRequestRes) => {
              value.entry.forEach((data) => {
                data.name = genMenuDesc(req.head.id, data.name);
                data.cause = getCauseByMap(data.cause);
              });
              reportList.push(...value.entry);
              if (value.moreFollows === 0) {
                resolve();
              }
            },
          })
          .catch((err) => {
            clientResult.code = IECCONSTANTS.CODE_ERROR;
            clientResult.msg = "获取历史报告错误:" + err;
            reject(err);
          });
      });
      clientResult.code = IECCONSTANTS.CODE_SUCCESS;
      clientResult.msg = "获取成功";
      clientResult.data = reportList;
      const notify: IECNotify = {
        type: "readOperateReport",
        data: clientResult,
      };
      logger.info("获取操作报告结束");
      sendMessageToUIByNotify(IEC_EVENT.REPORT_NOTIFY, notify, getMainWindow());
    } catch (err) {
      // 错误已在 catch 里处理
    }
    return clientResult;
  }

  // 获取审计报告
  async getAuditLogReportList(
    req: IECReq<AuditLogRequestData>
  ): Promise<IECResult<any>> {
    const { startTime, stopTime, entryAfter } = req.data;
    const clientResult = new IECResult<AuditLogEntry[]>();
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const reportList: AuditLogEntry[] = [];
    try {
      await new Promise<void>((resolve, reject) => {
        client
          .getAuditLog({
            startTime,
            stopTime,
            entryAfter,
            cb: (value: AuditLogRequestRes) => {
              reportList.push(...value.entry);
              if (value.moreFollows === 0) {
                resolve();
              }
            },
          })
          .catch((err) => {
            clientResult.code = IECCONSTANTS.CODE_ERROR;
            clientResult.msg = "获取历史报告错误:" + err;
            reject(err);
          });
      });
      clientResult.code = IECCONSTANTS.CODE_SUCCESS;
      clientResult.msg = "获取成功";
      clientResult.data = reportList;
      const notify: IECNotify = {
        type: "readAuditLogReport",
        data: clientResult,
      };
      logger.info("获取审计报告结束");
      sendMessageToUIByNotify(IEC_EVENT.REPORT_NOTIFY, notify, getMainWindow());
    } catch (err) {
      // 错误已在 catch 里处理
    }
    return clientResult;
  }

  // 导出通用报告
  async exportCommonReport(req: IECReq<IECReportExportParam>) {
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const { method, path, items } = req.data;
    const clientResult = new IECResult<string>();
    if (path.endsWith(".rpt")) {
      await saveRptData(method, clientResult, path, items);
    } else if (path.endsWith(".xlsx")) {
      saveExcelData(method, clientResult, path, items);
    }
    clientResult.data = path;
    return clientResult;
  }

  // 清除报告
  async clearReport(req: IECReq<undefined>) {
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const clientResult = new IECResult<boolean>();
    const method = req.data;
    if (method == "QueryHisFaultByTime") {
      client.getContext().clearFaultReports();
    } else if (method == "QueryOpReportByTime") {
      client.getContext().clearOperateReports();
    } else {
      client.getContext().clearAllEventReport();
    }
    clientResult.data = true;
    return clientResult;
  }

  // 刷新报告
  async refreshReport(req: IECReq<EventReportRequestData>) {
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const clientResult = new IECResult<EventReportEntry[]>();
    const type = req.data.type;
    const reportMap: Map<string, EventReportEntry[]> = client
      .getContext()
      .getEventReportMap();
    const list = reportMap.get(type);
    if (list != undefined && list.length > 0) {
      clientResult.data = list as unknown as EventReportEntry[];
    }
    return clientResult;
  }

  async refreshGroupReport(req: IECReq<FaultReportRequestData>) {
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const clientResult = new IECResult<FaultReportEntry[]>();
    const list: FaultReportFaultEntry[] = client.getContext().getFaultReports();
    list.sort((a, b) => {
      return b.faultNo! - a.faultNo!;
    });
    const resList: FaultReportEntry[] = [];
    if (list != undefined && list.length > 0) {
      list.forEach((obj) => {
        if (obj != undefined) {
          const lastChildren: FaultReportEntry[] = [];
          lastChildren.push(...obj.st);
          lastChildren.push(...obj.mx);
          const cloneChildren = cloneDeep(lastChildren);
          for (let i = 0; i < cloneChildren.length; i++) {
            const currObj = cloneChildren[i];
            if (currObj == undefined) {
              continue;
            }
            const valueName = cloneChildren[i].name
              ? cloneChildren[i].name
              : "";
            const valueField = cloneChildren[i].value
              ? "：" + cloneChildren[i].value
              : "";
            cloneChildren[i].name = i + 1 + "";
            cloneChildren[i].value =
              genMenuDesc(req.head.id, valueName) +
              valueField +
              genMenuUnit(req.head.id, valueName);
          }
          const children: FaultReportEntry = {
            id: obj.faultNo + 1001,
            name: "Idx",
            ret_ms: "Relative Time",
            value: "description",
            children: [...cloneChildren],
          };
          const root: FaultReportEntry = {
            id: obj.faultNo,
            name: "Fault No: " + obj.faultNo.toString(),
            ret_ms: "FaultStartTime",
            value: obj.faultStartTime,
            children: [children],
          };
          resList.push(root);
        }
      });
      clientResult.data = resList;
    }
    return clientResult;
  }

  async refreshOperateReport(req: IECReq<FaultReportRequestData>) {
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const clientResult = new IECResult<OperateReportEntry[]>();
    const list: OperateReportEntry[] = client.getContext().getOperateReports();
    if (list != undefined && list.length > 0) {
      clientResult.data = list as unknown as OperateReportEntry[];
    }
    return clientResult;
  }

  async refreshTripReport(req: IECReq<FaultReportRequestData>) {
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const clientResult = new IECResult<EventReportEntry[]>();
    const reportMap: Map<string, EventReportEntry[]> = client
      .getContext()
      .getEventReportMap();
    const list = reportMap.get("TRIP_TABLE");
    if (list != undefined && list.length > 0) {
      clientResult.data = list as unknown as EventReportEntry[];
    }
    return clientResult;
  }

  // 录波文件上招
  async uploadWave(req: IECReq<any>) {
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const { fileNo, time } = req.data;
    const requestData: FileDirRequestData = {
      pathName: "/wave/comtrade",
    };
    let fileName = "";
    const clientResult = new IECResult<string | unknown>();
    const res = await client.readFileDir(requestData);
    const files = res.data.fileEntry;
    const savePath = Ps.getExecDir();
    if (files.length == 0) {
      clientResult.code = IECCONSTANTS.CODE_ERROR;
      clientResult.msg = "录波文件未找到";
      return clientResult;
    }
    let fileList = files;
    let isSizeZero = false;
    if (fileNo != undefined) {
      let currTime = time.replace(/[-:]/g, "").replace(/[.\s]/g, "_");
      fileName = fileNo + "_" + currTime;
      fileList = filter(fileList, (obj) => {
        return obj.fileName.includes(fileName);
      }) as UpadRpcFileItem[];
      fileList.forEach((obj) => {
        obj.fileName = obj.fileName;
        if (obj.fileName.includes("cfg") && obj.fileSize == 0) {
          isSizeZero = true;
        }
      });
    } else {
      fileList = [];
    }
    if (fileList.length == 0) {
      clientResult.code = IECCONSTANTS.CODE_ERROR;
      clientResult.msg = "录波文件未找到";
      return clientResult;
    }
    if (isSizeZero) {
      clientResult.code = IECCONSTANTS.CODE_ERROR;
      clientResult.msg = "录波文件大小为0";
      return clientResult;
    }
    const fullPath = savePath + requestData.pathName + "/" + fileName;
    const param: FileReadRequestData = {
      fileItems: fileList,
      savePath: savePath,
      cb: (result: FileReadTransferData) => {
        if (result.status == FileReadTransferStatus.ERROR) {
          const data = { fileItem: result.fileItem, msg: result.status };
          const notify: IECNotify = {
            type: "fileUploadError",
            data: data,
          };
          sendMessageToUIByNotify(
            IEC_EVENT.REPORT_NOTIFY,
            notify,
            getMainWindow()
          );
          return;
        }
        const data = {
          fileItem: result.fileItem,
          status: result.status,
          percentage: result.progress,
        };
        const notify: IECNotify = {
          type: "fileUpload",
          data: data,
        };
        sendMessageToUIByNotify(
          IEC_EVENT.REPORT_NOTIFY,
          notify,
          getMainWindow()
        );
      },
    };
    await client
      .readFile(param)
      .catch((err) => {
        // 上传异常
        console.log(err);
      })
      .then(() => {
        clientResult.code = IECCONSTANTS.CODE_SUCCESS;
        clientResult.data = fullPath;
        clientResult.msg = "上传结束";
        const notify: IECNotify = {
          type: "fileUploadSuccess",
          data: clientResult,
        };
        sendMessageToUIByNotify(
          IEC_EVENT.REPORT_NOTIFY,
          notify,
          getMainWindow()
        );
      });

    clientResult.data = fullPath;
    return clientResult;
  }

  // 取消录波文件上招
  async cancelUpload(req: IECReq<undefined>) {
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const clientResult = new IECResult<boolean>();
    clientResult.data = true;
    return clientResult;
  }

  // 打开录波文件
  async openWaveFile(req: IECReq<IECRpcReportOpenWaveParam>) {
    const client = ClientDeviceGlobal.getInstance().getClient(req.head.id);
    if (!client || !client.isConnected()) {
      return getNoConnectResult();
    }
    const { exePath, filePath } = req.data;
    const realPath = filePath.replace(/\//g, "\\") + ".cfg";
    ChildProcess.spawn(exePath, [realPath]); // 将文件路径作为参数传递给exe
    const clientResult = new IECResult<boolean>();
    clientResult.data = true;
    return clientResult;
  }
}

const saveRptData = async (
  method: string,
  clientResult: IECResult<string>,
  path: string,
  items:
    | FaultReportEntry[]
    | EventReportEntry[]
    | OperateReportEntry[]
    | AuditLogEntry[]
): Promise<void> => {
  const res = dealRptData(method, items);
  if (isEmpty(res)) {
    clientResult.code = IECCONSTANTS.CODE_ERROR;
    clientResult.msg = "文件内容为空";
    return;
  }
  try {
    const write = await writeDataToFile(path, res);
    if (write) {
      clientResult.code = IECCONSTANTS.CODE_SUCCESS;
    }
  } catch (err) {
    clientResult.code = IECCONSTANTS.CODE_ERROR;
    clientResult.msg = (err as Error).message;
    logger.error("[report]保存报告rpt文件出错：", (err as Error).message);
  }
};

const dealRptData = (
  method: string,
  items:
    | EventReportEntry[]
    | FaultReportEntry[]
    | OperateReportEntry[]
    | AuditLogEntry[]
): string => {
  let res;
  switch (method) {
    case "QueryOpReportByTime":
      res = generateOperateRpt(items as OperateReportEntry[]);
      break;
    case "QueryHisEvtByTime":
      res = generateTripRpt(items as EventReportEntry[]);
      break;
    case "QueryHisFaultByTime":
      res = generateGroupRpt(items as FaultReportEntry[]);
      break;
    case "QueryAuditLogByTime":
      res = generateAuditLogRpt(items as AuditLogEntry[]);
      break;
    default:
      res = generateRpt(items as EventReportEntry[]);
      break;
  }
  return res;
};

const generateRpt = (items: EventReportEntry[]) => {
  let res = "";
  (items as EventReportEntry[]).forEach((obj) => {
    let data =
      undefinedToEmpty(obj.entryID) +
      "    " +
      undefinedToEmpty(obj.time) +
      "    ";
    if (obj.name) {
      data += obj.name + "    ";
    }
    if (obj.oldVal) {
      data += obj.oldVal + "    ";
    }
    if (obj.quality) {
      data += obj.quality + "    ";
    }
    if (obj.stVal) {
      data += obj.stVal + "    ";
    }
    res += data.trimEnd() + "\n";
  });
  return res;
};

const generateOperateRpt = (items: OperateReportEntry[]) => {
  let res = "";
  (items as OperateReportEntry[]).forEach((obj) => {
    let data =
      undefinedToEmpty(obj.entryID) +
      "    " +
      undefinedToEmpty(obj.name) +
      "    " +
      undefinedToEmpty(obj.time) +
      "    " +
      undefinedToEmpty(obj.conID) +
      "    " +
      undefinedToEmpty(obj.para) +
      "    " +
      undefinedToEmpty(obj.val) +
      "    " +
      undefinedToEmpty(obj.step) +
      "    " +
      undefinedToEmpty(obj.srcID) +
      "    " +
      undefinedToEmpty(obj.srcType) +
      "    " +
      undefinedToEmpty(obj.cause) +
      "    ";
    res += data.trimEnd() + "\n";
  });
  return res;
};

const generateGroupRpt = (items: FaultReportEntry[]) => {
  let res = "";
  (items as FaultReportEntry[]).forEach((obj) => {
    let data = obj.name + "    " + obj.value + "    " + obj.ret_ms + "\n";
    if (obj.children && obj.children.length > 0) {
      const child = obj.children[0];
      const childData =
        "  " + child.name + "    " + child.value + "    " + child.ret_ms + "\n";
      data += childData;
      if (child.children && child.children.length > 0) {
        child.children.forEach((obj) => {
          const lastChild = obj;
          const lastChildData =
            "    " +
            lastChild.name +
            "    " +
            undefinedToEmpty(lastChild.value) +
            "    " +
            undefinedToEmpty(lastChild.ret_ms) +
            "\n";
          data += lastChildData;
        });
      }
    }
    res += data.trimEnd() + "\n";
  });
  return res;
};

const generateTripRpt = (items: EventReportEntry[]) => {
  let res = "";
  (items as EventReportEntry[]).forEach((obj) => {
    let data =
      undefinedToEmpty(obj.entryID) +
      "    " +
      undefinedToEmpty(obj.time) +
      "    ";
    if (obj.name) {
      data += obj.name + "    ";
    }
    res += data.trimEnd() + "\n";
  });
  return res;
};

const generateAuditLogRpt = (items: AuditLogEntry[]) => {
  let res = "";
  (items as AuditLogEntry[]).forEach((obj) => {
    let data =
      undefinedToEmpty(obj.entryID) +
      "    " +
      undefinedToEmpty(obj.module) +
      "    " +
      undefinedToEmpty(obj.msg) +
      "    " +
      undefinedToEmpty(obj.type) +
      "    " +
      undefinedToEmpty(obj.level) +
      "    " +
      undefinedToEmpty(obj.orig) +
      "    " +
      undefinedToEmpty(obj.time) +
      "    " +
      undefinedToEmpty(obj.result) +
      "    " +
      undefinedToEmpty(obj.user) +
      "    ";
    res += data.trimEnd() + "\n";
  });
  return res;
};

const saveExcelData = (
  type: string,
  clientResult: IECResult<string>,
  path: string,
  items:
    | EventReportEntry[]
    | FaultReportEntry[]
    | OperateReportEntry[]
    | AuditLogEntry[]
) => {
  const bookNew = XLSX.utils.book_new();
  let workSheet;
  switch (type) {
    case "QueryOpReportByTime":
      workSheet = generateOperateWorkSheet(items as OperateReportEntry[]);
      break;
    case "QueryHisEvtByTime":
      workSheet = generateTripWorkSheet(items as EventReportEntry[]);
      break;
    case "QueryAuditLogByTime":
      workSheet = generateAuditLogWorkSheet(items as AuditLogEntry[]);
      break;
    default:
      workSheet = generateWorkSheet(items as EventReportEntry[]);
      break;
  }

  XLSX.utils.book_append_sheet(bookNew, workSheet, "report"); // 工作簿名称
  try {
    // 生成Excel文件
    XLSX.writeFileXLSX(bookNew, path, { bookType: "xlsx", type: "array" });
  } catch (err) {
    clientResult.code = IECCONSTANTS.CODE_ERROR;
    clientResult.msg = (err as Error).message;
    logger.error("[report]保存报告xlsx文件出错：", (err as Error).message);
  }
};

const generateWorkSheet = (data: EventReportEntry[]): XLSX.WorkSheet => {
  const tableData = [["报告编号", "报告时间", "描述"]]; // 表格表头
  data.forEach((item) => {
    let rowData: any[] = [];
    //导出内容的字段
    rowData = [item.entryID, item.time, item.name];
    tableData.push(rowData);
  });
  const workSheet = XLSX.utils.aoa_to_sheet(tableData);
  const columnWidth = [{ wch: 8 }, { wch: 35 }, { wch: 35 }];
  workSheet["!cols"] = columnWidth;
  return workSheet;
};

const generateOperateWorkSheet = (
  data: OperateReportEntry[]
): XLSX.WorkSheet => {
  const tableData = [
    [
      "报告编号",
      "名称",
      "时间",
      "操作地址",
      "操作参数",
      "值",
      "步骤",
      "源",
      "源类型",
      "结果",
    ],
  ]; // 表格表头
  data.forEach((item) => {
    let rowData: any[] = [];
    //导出内容的字段
    rowData = [
      item.entryID,
      item.name,
      item.time,
      item.conID,
      item.para,
      item.val,
      item.step,
      item.srcID,
      item.srcType,
      item.cause,
    ];
    tableData.push(rowData);
  });
  const workSheet = XLSX.utils.aoa_to_sheet(tableData);
  const columnWidth = [
    { wch: 10 },
    { wch: 35 },
    { wch: 35 },
    { wch: 20 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
    { wch: 15 },
  ];
  workSheet["!cols"] = columnWidth;
  return workSheet;
};

const generateTripWorkSheet = (data: EventReportEntry[]): XLSX.WorkSheet => {
  const tableData = [["报告编号", "报告时间", "描述"]]; // 表格表头
  data.forEach((item) => {
    let rowData: any[] = [];
    //导出内容的字段
    rowData = [item.entryID, item.time, item.name];
    tableData.push(rowData);
  });
  const workSheet = XLSX.utils.aoa_to_sheet(tableData);
  const columnWidth = [{ wch: 8 }, { wch: 35 }, { wch: 35 }];
  workSheet["!cols"] = columnWidth;
  return workSheet;
};

const generateAuditLogWorkSheet = (data: AuditLogEntry[]): XLSX.WorkSheet => {
  const tableData = [
    [
      "报告编号",
      "模块",
      "消息",
      "类型",
      "级别",
      "来源",
      "时间",
      "结果",
      "用户",
    ],
  ];
  data.forEach((item) => {
    let rowData: any[] = [
      item.entryID,
      item.module,
      item.msg,
      item.type,
      item.level,
      item.orig,
      item.time,
      item.result,
      item.user,
    ];
    tableData.push(rowData);
  });
  const workSheet = XLSX.utils.aoa_to_sheet(tableData);
  const columnWidth = [
    { wch: 10 },
    { wch: 20 },
    { wch: 40 },
    { wch: 10 },
    { wch: 10 },
    { wch: 20 },
    { wch: 25 },
    { wch: 15 },
    { wch: 15 },
  ];
  workSheet["!cols"] = columnWidth;
  return workSheet;
};

const genMenuDesc = (id: string, name: string) => {
  const desc = debugInfoMenuService.getDebugItemMapVal(id, name);
  if (desc === null) {
    return name;
  }
  return desc;
};

const genMenuUnit = (id: string, name: string) => {
  const obj = debugInfoMenuService.getDebugItemObjMapVal(id, name);
  if (obj == null) {
    return "";
  }
  return obj.unit || "";
};

const getCauseByMap = (cause: string): string => {
  if (cause == "" || cause == undefined) {
    return cause;
  }
  const causeCh = AddCauseDescMap.get(cause.toUpperCase());
  if (causeCh != undefined) {
    return causeCh;
  }
  return cause;
};

ReportService.toString = () => "[class ReportService]";
const reportService = new ReportService();

export { ReportService, reportService };
